#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import mysql.connector
from mysql.connector import Error
import re
from difflib import SequenceMatcher

def get_database_connection():
    """
    Tạo kết nối database
    """
    try:
        connection = mysql.connector.connect(
            host='127.0.0.1',
            port=3306,
            database='urbox',
            user='root',
            password='root'
        )
        
        if connection.is_connected():
            print("✅ Kết nối database thành công!")
            return connection
            
    except Error as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def normalize_province_name(name):
    """
    Chuẩn hóa tên tỉnh để so sánh
    """
    # Loại bỏ các tiền tố
    name = re.sub(r'^(Tỉnh|Thành phố|TP\.)\s+', '', name, flags=re.IGNORECASE)
    
    # Chuẩn hóa một số tên đặc biệt
    replacements = {
        'TP. Hà Nội': '<PERSON><PERSON>',
        'TP. Hải Phòng': '<PERSON><PERSON><PERSON>', 
        'TP. Đà Nẵng': 'Đà Nẵng',
        'TP. Huế': 'Hu<PERSON>',
        'TP. <PERSON>í <PERSON>': '<PERSON><PERSON> Chí <PERSON>',
        'TP. Cần Thơ': 'Cần Thơ',
        'Khánh Hoà': 'Khánh Hòa',
        'TPHCM': 'Hồ Chí Minh'
    }
    
    for old, new in replacements.items():
        if old in name:
            name = name.replace(old, new)
    
    return name.strip()

def similarity(a, b):
    """
    Tính độ tương tự giữa 2 chuỗi
    """
    return SequenceMatcher(None, a.lower(), b.lower()).ratio()

def find_similar_provinces(target_name, province_list, threshold=0.6):
    """
    Tìm tỉnh có tên tương tự
    """
    similar = []
    normalized_target = normalize_province_name(target_name)
    
    for province in province_list:
        normalized_db = normalize_province_name(province['title'])
        sim_score = similarity(normalized_target, normalized_db)
        
        if sim_score >= threshold:
            similar.append({
                'province': province,
                'similarity': sim_score,
                'normalized_target': normalized_target,
                'normalized_db': normalized_db
            })
    
    # Sắp xếp theo độ tương tự giảm dần
    similar.sort(key=lambda x: x['similarity'], reverse=True)
    return similar

def search_missing_provinces(connection):
    """
    Tìm kiếm 2 tỉnh còn thiếu: Hòa Bình và TP. Huế
    """
    missing_provinces = ['Hòa Bình', 'TP. Huế']
    
    try:
        cursor = connection.cursor(dictionary=True)
        
        # Lấy tất cả tỉnh cũ
        cursor.execute("""
            SELECT id, pti_id, title, new_pti_id 
            FROM ___province 
            WHERE is_new = 1 
            ORDER BY title
        """)
        old_provinces = cursor.fetchall()
        
        print(f"🔍 TÌM KIẾM 2 TỈNH CÒN THIẾU")
        print("=" * 50)
        
        for missing in missing_provinces:
            print(f"\n🎯 Tìm kiếm: {missing}")
            print("-" * 30)
            
            # Tìm exact match trước
            exact_matches = []
            for province in old_provinces:
                if normalize_province_name(missing) == normalize_province_name(province['title']):
                    exact_matches.append(province)
            
            if exact_matches:
                print("✅ Tìm thấy exact match:")
                for match in exact_matches:
                    print(f"  - ID: {match['id']}, Title: {match['title']}, PTI_ID: {match['pti_id']}")
            else:
                print("❌ Không tìm thấy exact match")
                
                # Tìm similar matches
                similar_matches = find_similar_provinces(missing, old_provinces, threshold=0.5)
                
                if similar_matches:
                    print("🔍 Tỉnh có tên tương tự:")
                    for match in similar_matches[:5]:  # Top 5
                        province = match['province']
                        sim = match['similarity']
                        print(f"  - {province['title']} (ID: {province['id']}, PTI_ID: {province['pti_id']}) - Độ tương tự: {sim:.2f}")
                        print(f"    Target: '{match['normalized_target']}' vs DB: '{match['normalized_db']}'")
                else:
                    print("❌ Không tìm thấy tỉnh tương tự")
            
            # Tìm kiếm theo từ khóa
            print(f"\n🔎 Tìm kiếm theo từ khóa trong '{missing}':")
            keywords = missing.split()
            keyword_matches = []
            
            for keyword in keywords:
                if len(keyword) >= 2:  # Chỉ tìm từ khóa có ít nhất 2 ký tự
                    for province in old_provinces:
                        if keyword.lower() in province['title'].lower():
                            keyword_matches.append((province, keyword))
            
            if keyword_matches:
                print("📝 Tỉnh chứa từ khóa:")
                for province, keyword in keyword_matches:
                    print(f"  - {province['title']} (chứa '{keyword}') - ID: {province['id']}, PTI_ID: {province['pti_id']}")
            else:
                print("❌ Không tìm thấy tỉnh chứa từ khóa")
        
        # Hiển thị tất cả tỉnh cũ để tham khảo
        print(f"\n📋 TẤT CẢ {len(old_provinces)} TỈNH CŨ TRONG DATABASE:")
        print("=" * 50)
        
        for i, province in enumerate(old_provinces, 1):
            status = "✅ Đã mapping" if province['new_pti_id'] else "❌ Chưa mapping"
            print(f"{i:2d}. {province['title']} (ID: {province['id']}, PTI_ID: {province['pti_id']}) - {status}")
        
        # Đếm số tỉnh đã và chưa mapping
        mapped_count = sum(1 for p in old_provinces if p['new_pti_id'])
        unmapped_count = len(old_provinces) - mapped_count
        
        print(f"\n📊 THỐNG KÊ:")
        print(f"- Tổng số tỉnh cũ: {len(old_provinces)}")
        print(f"- Đã mapping: {mapped_count}")
        print(f"- Chưa mapping: {unmapped_count}")
        
        if unmapped_count > 0:
            print(f"\n❌ CÁC TỈNH CHƯA MAPPING:")
            for province in old_provinces:
                if not province['new_pti_id']:
                    print(f"  - {province['title']} (ID: {province['id']}, PTI_ID: {province['pti_id']})")
                    
    except Error as e:
        print(f"❌ Lỗi truy vấn database: {e}")

def main():
    """
    Hàm chính
    """
    print("🔍 TÌM KIẾM TỈNH CÒN THIẾU")
    print("=" * 50)
    
    # Kết nối database
    connection = get_database_connection()
    if not connection:
        return
    
    try:
        search_missing_provinces(connection)
        
    finally:
        if connection.is_connected():
            connection.close()
            print("\n🔌 Đã đóng kết nối database")

if __name__ == "__main__":
    main()
