#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import mysql.connector
from mysql.connector import Error
import re
from tabulate import tabulate

def read_csv_mapping(csv_file):
    """
    Đọc file CSV để lấy mapping tỉnh cũ -> tỉnh mới
    """
    mapping = {}
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            for row in reader:
                province_new = row['province'].strip()
                merge_from = row['merge_from_province'].strip()
                
                # Tách các tỉnh cũ (phân cách bằng ' + ')
                old_provinces = [p.strip() for p in merge_from.split(' + ')]
                mapping[province_new] = old_provinces
                
    except Exception as e:
        print(f"❌ Lỗi đọc file CSV: {e}")
        return {}
    
    return mapping

def normalize_province_name(name):
    """
    Chuẩn hóa tên tỉnh để so sánh
    """
    # Loại bỏ các tiền tố
    name = re.sub(r'^(Tỉnh|Thành phố|TP\.)\s+', '', name, flags=re.IGNORECASE)
    
    # Chuẩn hóa một số tên đặc biệt
    replacements = {
        'TP. Hà Nội': 'Hà Nội',
        'TP. Hải Phòng': 'Hải Phòng', 
        'TP. Đà Nẵng': 'Đà Nẵng',
        'TP. Huế': 'Huế',
        'TP. Hồ Chí Minh': 'Hồ Chí Minh',
        'TP. Cần Thơ': 'Cần Thơ',
        'Khánh Hoà': 'Khánh Hòa',
        'TPHCM': 'Hồ Chí Minh'
    }
    
    for old, new in replacements.items():
        if old in name:
            name = name.replace(old, new)
    
    return name.strip()

def get_database_connection():
    """
    Tạo kết nối database
    """
    try:
        connection = mysql.connector.connect(
            host='127.0.0.1',
            port=3306,
            database='urbox',
            user='root',
            password='root'
        )
        
        if connection.is_connected():
            print("✅ Kết nối database thành công!")
            return connection
            
    except Error as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def check_province_structure(connection):
    """
    Kiểm tra cấu trúc bảng ___province
    """
    print("\n🔍 KIỂM TRA CẤU TRÚC BẢNG ___province")
    print("=" * 50)
    
    try:
        cursor = connection.cursor()
        
        # Kiểm tra cấu trúc bảng
        cursor.execute("DESCRIBE ___province")
        columns = cursor.fetchall()
        
        print("📋 Cấu trúc bảng:")
        headers = ["Field", "Type", "Null", "Key", "Default", "Extra"]
        print(tabulate(columns, headers=headers, tablefmt="grid"))
        
        # Kiểm tra có cột new_pti_id không
        column_names = [col[0] for col in columns]
        if 'new_pti_id' not in column_names:
            print("\n⚠️  CẢNH BÁO: Không tìm thấy cột 'new_pti_id'!")
            print("   Bạn có thể cần tạo cột này trước:")
            print("   ALTER TABLE ___province ADD COLUMN new_pti_id INT DEFAULT NULL;")
        else:
            print("\n✅ Cột 'new_pti_id' đã tồn tại")
            
    except Error as e:
        print(f"❌ Lỗi kiểm tra cấu trúc: {e}")

def check_province_data(connection):
    """
    Kiểm tra dữ liệu trong bảng ___province
    """
    print("\n📊 KIỂM TRA DỮ LIỆU BẢNG ___province")
    print("=" * 50)
    
    try:
        cursor = connection.cursor(dictionary=True)
        
        # Đếm tổng số bản ghi
        cursor.execute("SELECT COUNT(*) as total FROM ___province")
        total = cursor.fetchone()['total']
        print(f"📈 Tổng số bản ghi: {total}")
        
        # Đếm theo is_new
        cursor.execute("""
            SELECT is_new, COUNT(*) as count 
            FROM ___province 
            GROUP BY is_new 
            ORDER BY is_new
        """)
        is_new_stats = cursor.fetchall()
        
        print("\n📊 Phân bố theo is_new:")
        for stat in is_new_stats:
            status = "Tỉnh cũ" if stat['is_new'] == 1 else "Tỉnh mới" if stat['is_new'] == 2 else f"Khác ({stat['is_new']})"
            print(f"  - is_new = {stat['is_new']} ({status}): {stat['count']} bản ghi")
        
        # Lấy mẫu tỉnh cũ
        cursor.execute("""
            SELECT id, pti_id, title, new_pti_id
            FROM ___province
            WHERE is_new = 1
            LIMIT 10
        """)
        old_provinces = cursor.fetchall()

        print(f"\n📋 Mẫu 10 tỉnh cũ (is_new=1):")
        if old_provinces:
            headers = ["ID", "PTI_ID", "Title", "New_PTI_ID"]
            data = [[p['id'], p['pti_id'], p['title'], p['new_pti_id']] for p in old_provinces]
            print(tabulate(data, headers=headers, tablefmt="grid"))
        
        # Lấy mẫu tỉnh mới
        cursor.execute("""
            SELECT id, pti_id, title 
            FROM ___province 
            WHERE is_new = 2 
            LIMIT 10
        """)
        new_provinces = cursor.fetchall()
        
        print(f"\n📋 Mẫu 10 tỉnh mới (is_new=2):")
        if new_provinces:
            headers = ["ID", "PTI_ID", "Title"]
            data = [[p['id'], p['pti_id'], p['title']] for p in new_provinces]
            print(tabulate(data, headers=headers, tablefmt="grid"))
            
    except Error as e:
        print(f"❌ Lỗi kiểm tra dữ liệu: {e}")

def check_csv_vs_database(connection, csv_file):
    """
    So sánh dữ liệu CSV với database
    """
    print("\n🔄 SO SÁNH DỮ LIỆU CSV VỚI DATABASE")
    print("=" * 50)
    
    # Đọc CSV
    csv_mapping = read_csv_mapping(csv_file)
    if not csv_mapping:
        print("❌ Không thể đọc file CSV!")
        return
    
    print(f"📄 File CSV có {len(csv_mapping)} tỉnh mới")
    
    try:
        cursor = connection.cursor(dictionary=True)
        
        # Lấy tỉnh mới từ database
        cursor.execute("""
            SELECT id, pti_id, title 
            FROM ___province 
            WHERE is_new = 2 
            ORDER BY title
        """)
        db_new_provinces = cursor.fetchall()
        
        # Lấy tỉnh cũ từ database
        cursor.execute("""
            SELECT id, pti_id, title 
            FROM ___province 
            WHERE is_new = 1 
            ORDER BY title
        """)
        db_old_provinces = cursor.fetchall()
        
        print(f"🗄️  Database có {len(db_new_provinces)} tỉnh mới và {len(db_old_provinces)} tỉnh cũ")
        
        # Kiểm tra matching
        matched_new = []
        unmatched_new = []
        matched_old = []
        unmatched_old = []
        
        # Tạo dict để tra cứu nhanh
        db_new_dict = {normalize_province_name(p['title']): p for p in db_new_provinces}
        db_old_dict = {normalize_province_name(p['title']): p for p in db_old_provinces}
        
        for csv_new_name, csv_old_names in csv_mapping.items():
            # Kiểm tra tỉnh mới
            normalized_new = normalize_province_name(csv_new_name)
            if normalized_new in db_new_dict:
                matched_new.append((csv_new_name, db_new_dict[normalized_new]['title']))
            else:
                unmatched_new.append(csv_new_name)
            
            # Kiểm tra tỉnh cũ
            for csv_old_name in csv_old_names:
                normalized_old = normalize_province_name(csv_old_name)
                if normalized_old in db_old_dict:
                    matched_old.append((csv_old_name, db_old_dict[normalized_old]['title']))
                else:
                    unmatched_old.append(csv_old_name)
        
        # Báo cáo kết quả
        print(f"\n✅ TỈNH MỚI - Khớp: {len(matched_new)}/{len(csv_mapping)}")
        if unmatched_new:
            print("❌ Tỉnh mới không tìm thấy trong DB:")
            for name in unmatched_new:
                print(f"  - {name}")
        
        print(f"\n✅ TỈNH CŨ - Khớp: {len(matched_old)}")
        if unmatched_old:
            print("❌ Tỉnh cũ không tìm thấy trong DB:")
            for name in unmatched_old[:10]:  # Chỉ hiển thị 10 đầu
                print(f"  - {name}")
            if len(unmatched_old) > 10:
                print(f"  ... và {len(unmatched_old) - 10} tỉnh khác")
        
        # Hiển thị một số mapping mẫu
        print(f"\n📋 MẪU MAPPING SẼ ĐƯỢC THỰC HIỆN:")
        count = 0
        for csv_new_name, csv_old_names in csv_mapping.items():
            if count >= 5:  # Chỉ hiển thị 5 mẫu
                break
                
            normalized_new = normalize_province_name(csv_new_name)
            if normalized_new in db_new_dict:
                new_province = db_new_dict[normalized_new]
                print(f"\n🎯 {csv_new_name} (pti_id: {new_province['pti_id']})")
                
                for csv_old_name in csv_old_names:
                    normalized_old = normalize_province_name(csv_old_name)
                    if normalized_old in db_old_dict:
                        old_province = db_old_dict[normalized_old]
                        print(f"  ← {old_province['title']} (id: {old_province['id']})")
                    else:
                        print(f"  ❌ {csv_old_name} (không tìm thấy)")
                count += 1
                
    except Error as e:
        print(f"❌ Lỗi so sánh dữ liệu: {e}")

def main():
    """
    Hàm chính
    """
    print("🔍 KIỂM TRA DỮ LIỆU PROVINCE MAPPING")
    print("=" * 50)
    
    # Kết nối database
    connection = get_database_connection()
    if not connection:
        return
    
    try:
        # Kiểm tra cấu trúc bảng
        check_province_structure(connection)
        
        # Kiểm tra dữ liệu
        check_province_data(connection)
        
        # So sánh với CSV
        check_csv_vs_database(connection, 'tinhthanh-sapnhap.csv')
        
        print(f"\n🎉 HOÀN THÀNH KIỂM TRA!")
        print("💡 Nếu mọi thứ OK, bạn có thể chạy script update_province_mapping.py")
        
    finally:
        if connection.is_connected():
            connection.close()
            print("\n🔌 Đã đóng kết nối database")

if __name__ == "__main__":
    main()
