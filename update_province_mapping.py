#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import mysql.connector
from mysql.connector import Error
import re

def read_csv_mapping(csv_file):
    """
    Đọc file CSV để lấy mapping tỉnh cũ -> tỉnh mới
    
    Returns:
        dict: {tỉnh_mới: [danh_sách_tỉnh_cũ]}
    """
    mapping = {}
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            for row in reader:
                province_new = row['province'].strip()
                merge_from = row['merge_from_province'].strip()
                
                # Tách các tỉnh cũ (phân cách bằng ' + ')
                old_provinces = [p.strip() for p in merge_from.split(' + ')]
                mapping[province_new] = old_provinces
                
                print(f"Tỉnh mới: {province_new}")
                print(f"  Từ các tỉnh cũ: {old_provinces}")
                print()
                
    except Exception as e:
        print(f"Lỗi đọc file CSV: {e}")
        return {}
    
    return mapping

def normalize_province_name(name):
    """
    Chuẩn hóa tên tỉnh để so sánh
    """
    # Loại bỏ các tiền tố
    name = re.sub(r'^(Tỉnh|Thành phố|TP\.)\s+', '', name, flags=re.IGNORECASE)
    
    # Chuẩn hóa một số tên đặc biệt
    replacements = {
        'TP. Hà Nội': 'Hà Nội',
        'TP. Hải Phòng': 'Hải Phòng', 
        'TP. Đà Nẵng': 'Đà Nẵng',
        'TP. Huế': 'Huế',
        'TP. Hồ Chí Minh': 'Hồ Chí Minh',
        'TP. Cần Thơ': 'Cần Thơ',
        'Khánh Hoà': 'Khánh Hòa',
        'TPHCM': 'Hồ Chí Minh'
    }
    
    for old, new in replacements.items():
        if old in name:
            name = name.replace(old, new)
    
    return name.strip()

def get_database_connection():
    """
    Tạo kết nối database
    """
    try:
        connection = mysql.connector.connect(
            host='127.0.0.1',
            port=3306,
            database='urbox',
            user='root',
            password='root'
        )
        
        if connection.is_connected():
            print("Kết nối database thành công!")
            return connection
            
    except Error as e:
        print(f"Lỗi kết nối database: {e}")
        return None

def get_provinces_from_db(connection):
    """
    Lấy danh sách tỉnh từ database
    
    Returns:
        tuple: (provinces_old, provinces_new)
    """
    try:
        cursor = connection.cursor(dictionary=True)
        
        # Lấy tỉnh cũ (is_new = 1)
        cursor.execute("""
            SELECT id, pti_id, title, new_pti_id
            FROM ___province
            WHERE is_new = 1
            ORDER BY title
        """)
        provinces_old = cursor.fetchall()
        
        # Lấy tỉnh mới (is_new = 2)  
        cursor.execute("""
            SELECT id, pti_id, title 
            FROM ___province 
            WHERE is_new = 2 
            ORDER BY title
        """)
        provinces_new = cursor.fetchall()
        
        print(f"Tìm thấy {len(provinces_old)} tỉnh cũ và {len(provinces_new)} tỉnh mới")
        
        return provinces_old, provinces_new
        
    except Error as e:
        print(f"Lỗi truy vấn database: {e}")
        return [], []

def find_matching_province(province_name, province_list):
    """
    Tìm tỉnh khớp trong danh sách
    """
    normalized_name = normalize_province_name(province_name)
    
    for province in province_list:
        db_name = normalize_province_name(province['title'])
        
        if normalized_name == db_name:
            return province
            
        # Thử so sánh không dấu
        if normalized_name.lower() == db_name.lower():
            return province
    
    return None

def update_province_mapping(connection, csv_file):
    """
    Cập nhật mapping tỉnh cũ -> tỉnh mới
    """
    # Đọc mapping từ CSV
    csv_mapping = read_csv_mapping(csv_file)
    if not csv_mapping:
        print("Không thể đọc file CSV!")
        return
    
    # Lấy dữ liệu từ database
    provinces_old, provinces_new = get_provinces_from_db(connection)
    if not provinces_old or not provinces_new:
        print("Không thể lấy dữ liệu từ database!")
        return
    
    # Tạo dict để tra cứu nhanh tỉnh mới
    new_provinces_dict = {normalize_province_name(p['title']): p for p in provinces_new}
    
    updates = []
    not_found = []
    
    # Xử lý từng mapping
    for new_province_name, old_province_names in csv_mapping.items():
        # Tìm tỉnh mới trong database
        new_province = find_matching_province(new_province_name, provinces_new)
        
        if not new_province:
            print(f"❌ Không tìm thấy tỉnh mới: {new_province_name}")
            not_found.append(new_province_name)
            continue
            
        new_pti_id = new_province['pti_id']
        print(f"✅ Tỉnh mới: {new_province['title']} (pti_id: {new_pti_id})")
        
        # Tìm các tỉnh cũ tương ứng
        for old_province_name in old_province_names:
            old_province = find_matching_province(old_province_name, provinces_old)
            
            if old_province:
                updates.append({
                    'id': old_province['id'],
                    'old_title': old_province['title'],
                    'new_pti_id': new_pti_id,
                    'new_title': new_province['title']
                })
                print(f"  ➡️ Cập nhật: {old_province['title']} -> new_pit_id = {new_pti_id}")
            else:
                print(f"  ❌ Không tìm thấy tỉnh cũ: {old_province_name}")
                not_found.append(old_province_name)
    
    # Hiển thị tổng kết
    print(f"\n📊 TỔNG KẾT:")
    print(f"- Số bản ghi cần cập nhật: {len(updates)}")
    print(f"- Số tỉnh không tìm thấy: {len(not_found)}")
    
    if not_found:
        print(f"\n❌ Danh sách tỉnh không tìm thấy:")
        for name in not_found:
            print(f"  - {name}")
    
    # Xác nhận cập nhật
    if updates:
        print(f"\n🔄 Chuẩn bị cập nhật {len(updates)} bản ghi...")
        
        try:
            cursor = connection.cursor()
            
            for update in updates:
                cursor.execute("""
                    UPDATE ___province
                    SET new_pti_id = %s
                    WHERE id = %s
                """, (update['new_pti_id'], update['id']))
                
                print(f"✅ Đã cập nhật: {update['old_title']} -> new_pit_id = {update['new_pti_id']}")
            
            connection.commit()
            print(f"\n🎉 Hoàn thành cập nhật {len(updates)} bản ghi!")
            
        except Error as e:
            print(f"❌ Lỗi cập nhật database: {e}")
            connection.rollback()

def main():
    """
    Hàm chính
    """
    print("🚀 BẮT ĐẦU CẬP NHẬT PROVINCE MAPPING")
    print("=" * 50)
    
    # Kết nối database
    connection = get_database_connection()
    if not connection:
        return
    
    try:
        # Cập nhật mapping
        update_province_mapping(connection, 'tinhthanh-sapnhap.csv')
        
    finally:
        if connection.is_connected():
            connection.close()
            print("\n🔌 Đã đóng kết nối database")

if __name__ == "__main__":
    main()
